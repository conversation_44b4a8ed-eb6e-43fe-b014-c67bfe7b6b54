The model you use for for Chat mode will be

- used with Edit mode by default but can be switched
- always used with Agent mode if the model supports tool calling

## Recommended Models

Our strong recommendation is to use [Claude Sonnet 4](https://hub.continue.dev/anthropic/claude-4-sonnet) from Anthropic.

Its strong tool calling and reasoning capabilities make it the best model for Agent mode.

1. Get your API key from [Anthropic](https://console.anthropic.com/)
2. Add [Claude Sonnet 4](https://hub.continue.dev/anthropic/claude-4-sonnet) to your assistant on Continue Hub
3. Add `ANTHROPIC_API_KEY` as a [User Secret](https://docs.continue.dev/hub/secrets/secret-types#user-secrets) on Continue Hub [here](https://hub.continue.dev/settings/secrets)
4. Click `Reload config` in the assistant selector in the Continue IDE extension

#### Other hosted models

These models have varying tool calling and reasoning capabilities.

[Gemini 2.5 Pro](https://hub.continue.dev/google/gemini-2.5-pro) from Google

1. Get your API key from [Google AI Studio](https://aistudio.google.com)
2. Add [Gemini 2.5 Pro](https://hub.continue.dev/google/gemini-2.5-pro) to your assistant on Continue Hub
3. Add `GEMINI_API_KEY` as a [User Secret](https://docs.continue.dev/hub/secrets/secret-types#user-secrets) on Continue Hub [here](https://hub.continue.dev/settings/secrets)
4. Click `Reload config` in the assistant selector in the Continue IDE extension

[o3](https://hub.continue.dev/openai/o3) from OpenAI

1. Get your API key from [OpenAI](https://platform.openai.com)
2. Add [o3](https://hub.continue.dev/openai/o3) to your assistant on Continue Hub
3. Add `OPENAI_API_KEY` as a [User Secret](https://docs.continue.dev/hub/secrets/secret-types#user-secrets) on Continue Hub [here](https://hub.continue.dev/settings/secrets)
4. Click `Reload config` in the assistant selector in the Continue IDE extension

[Grok 4](https://hub.continue.dev/xai/grok-4) from xAI

1. Get your API key from [xAI](https://console.x.ai/)
2. Add [Grok 4](https://hub.continue.dev/xai/grok-4) to your assistant on Continue Hub
3. Add `XAI_API_KEY` as a [User Secret](https://docs.continue.dev/hub/secrets/secret-types#user-secrets) on Continue Hub [here](https://hub.continue.dev/settings/secrets)
4. Click `Reload config` in the assistant selector in the Continue IDE extension

[Devstral Medium](https://hub.continue.dev/mistral/devstral-medium) from Mistral AI

1. Get your API key from [Mistral AI](https://console.mistral.ai/)
2. Add [Devstral Medium](https://hub.continue.dev/mistral/devstral-medium) to your assistant on Continue Hub
3. Add `MISTRAL_API_KEY` as a [User Secret](https://docs.continue.dev/hub/secrets/secret-types#user-secrets) on Continue Hub [here](https://hub.continue.dev/settings/secrets)
4. Click `Reload config` in the assistant selector in the Continue IDE extension

[Kimi K2](hub.continue.dev/togetherai/kimi-k2-instruct) from Moonshot AI

1. Get your API key from [TogetherAI](https://api.together.ai)
2. Add [Kimi K2](hub.continue.dev/togetherai/kimi-k2-instruct) to your assistant on Continue Hub
3. Add `TOGETHER_API_KEY` as a [User Secret](https://docs.continue.dev/hub/secrets/secret-types#user-secrets) on Continue Hub [here](https://hub.continue.dev/settings/secrets)
4. Click `Reload config` in the assistant selector in the Continue IDE extension

#### Local models

These models can be run on your computer if you have enough VRAM.

Their limited tool calling and reasoning capabilities will make it challenging to use Agent mode.

[Qwen2.5-Coder 7B](https://hub.continue.dev/ollama/qwen2.5-coder-7b) from Qwen

1. Add [Qwen2.5-Coder 7B](https://hub.continue.dev/ollama/qwen2.5-coder-7b) to your assistant on Continue Hub
2. Run the model with [Ollama](https://docs.continue.dev/guides/ollama-guide#using-ollama-with-continue-a-developers-guide)
3. Click `Reload config` in the assistant selector in the Continue IDE extension

[Gemma 3 4B](https://hub.continue.dev/ollama/gemma3-4b) from Google

1. Add [Gemma 3 4B](https://hub.continue.dev/ollama/gemma3-4b) to your assistant on Continue Hub
2. Run the model with [Ollama](https://docs.continue.dev/guides/ollama-guide#using-ollama-with-continue-a-developers-guide)
3. Click `Reload config` in the assistant selector in the Continue IDE extension

[Llama 3.1 8B](https://hub.continue.dev/ollama/llama3.1-8b) from Meta

1. Add [Llama 3.1 8B](https://hub.continue.dev/ollama/llama3.1-8b) to your assistant on Continue Hub
2. Run the model with [Ollama](https://docs.continue.dev/guides/ollama-guide#using-ollama-with-continue-a-developers-guide)
3. Click `Reload config` in the assistant selector in the Continue IDE extension

For more detailed setup instructions, see [here](/getting-started/install#setup-your-first-model)
