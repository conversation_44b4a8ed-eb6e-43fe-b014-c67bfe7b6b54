---
title: Continue Documentation MCP Server
description: Set up an MCP server to search Continue documentation
keywords: [mcp, documentation, search, mintlify, reference]
---

The continue-docs MCP Server allows you to search and retrieve information from the Continue documentation directly within your agent conversations. This is powered by Mintlify's MCP server generation.

## Installation

### Step 1: Install the MCP Server

Run the following command to install the continue-docs MCP server:

```bash
npx mint-mcp add docs.continue.dev
```

When prompted, select "All" from the selection menu:

![mintlify cli selection tree](/images/mintlify-cli-selection-tree.png)

This command will:

- Download and install the MCP server locally
- Set up the search tool for Continue documentation
- Create the necessary configuration files

<Info>
Take note of the installation path displayed after running the command above. You'll need to replace in the configuration below with your actual installation path.

It will look like this: `/path/to/user/.mcp/docs.continue.dev/src/index.js`

</Info>

### Step 2: Configure Continue

1. Create a folder called `.continue/mcpServers` at the top level of your workspace
2. Add a file called `continue-docs-mcp.yaml` to this folder
3. Write the following contents and save:

````yaml title=".continue/mcpServers/continue-docs-mcp.yaml"
```yaml title=".continue/mcpServers/continue-docs-mcp.yaml"
name: Continue Documentation MCP
version: 0.0.1
schema: v1
mcpServers:
  - name: Continue Docs Search
    command: node
    args:
      - "/path/to/user/.mcp/docs.continue.dev/src/index.js"
````

### Step 3: Enable Agent Mode

MCP servers only work in agent mode. Make sure to switch to agent mode in Continue before testing.

## Usage Examples

Once configured, you can use the MCP server to search Continue documentation:

### Model Configuration Help

```
Search for model setup instructions in the Continue docs
```

### Context Providers

```
Find documentation about context providers and how to configure them
```

### Autocomplete Setup

```
How do I configure autocomplete in Continue?
```

### Slash Commands

```
Search for information about custom slash commands
```

### Troubleshooting

```
Find troubleshooting information for Continue extension issues
```

## Test Plan

Use this test plan to verify your MCP server is working correctly:

### Prerequisites

- Continue extension is installed and running
- Agent mode is enabled
- MCP server configuration file exists in `.continue/mcpServers/`

### Test Cases

#### 1. Basic Documentation Search

- **Query**: "Search the Continue docs for information about model setup"
- **Expected**: Returns structured information about configuring models
- **Pass/Fail**: ☐

#### 2. Context Provider Documentation

- **Query**: "Find documentation about context providers in Continue"
- **Expected**: Returns information about available context providers
- **Pass/Fail**: ☐

#### 3. Feature-Specific Search

- **Query**: "How do I configure autocomplete in Continue?"
- **Expected**: Returns autocomplete setup instructions
- **Pass/Fail**: ☐

#### 4. MCP Documentation Search

- **Query**: "Search for MCP server setup instructions"
- **Expected**: Returns MCP configuration information
- **Pass/Fail**: ☐

## Troubleshooting

### MCP Server Not Loading

1. **Check configuration**: Ensure your YAML configuration uses the npx command approach
2. **Verify installation**: Run `npx mint-mcp add continue-docs` to confirm the server can be accessed
3. **Check agent mode**: MCP servers only work in agent mode
4. **Restart Continue**: Try restarting the Continue extension

### No Search Results

1. **Verify connection**: The MCP server needs internet access to search the documentation
2. **Check query format**: Try rephrasing your search query
3. **Test with known topics**: Search for well-documented features like "model configuration"

### Permission Issues

If you encounter permission errors, ensure npx has proper permissions:

```bash
npm config get prefix
```

## Advanced Configuration

### Using Environment Variables

You can configure the MCP server with environment variables:

## Related Documentation

- [MCP Overview](/customize/deep-dives/mcp)
- [Agent Mode](/features/agent/quick-start)
- [Configuration](/customize/overview)
