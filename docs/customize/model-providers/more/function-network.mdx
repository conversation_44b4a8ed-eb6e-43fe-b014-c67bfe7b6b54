---
title: "Function Network"
---

---

title: Function Network
slug: ../function-network

---

> Private, Affordable User-Owned AI

<Info>

To get an API key, login to the Function Network Developer Platform. If you don't have an account, you can create one [here](https://www.function.network/join-waitlist).

</Info>

## Chat model

Function Network supports a number of models for chat. We recommend using LLama 3.1 70b or Qwen2.5-Coder-32B-Instruct.

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Llama 3.1 70b
      provider: function-network
      model: meta/llama-3.1-70b-instruct
      apiKey: <YOUR_FUNCTION_NETWORK_API_KEY>
      roles:
        - chat
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "title": "Llama 3.1 70b",
        "provider": "function-network",
        "model": "meta/llama-3.1-70b-instruct",
        "apiKey": "<YOUR_FUNCTION_NETWORK_API_KEY>"
      }
    ]
  }
  ```
  </Tab>
</Tabs>

[Click here](https://docs.function.network/models-supported/chat-and-code-completion) to see a list of chat model providers.

## Autocomplete model

Function Network supports a number of models for autocomplete. We recommend using Llama 3.1 8b or Qwen2.5-Coder-1.5B.

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Deepseek Coder 6.7b
      provider: function-network
      model: thebloke/deepseek-coder-6.7b-base-awq
      apiKey: <YOUR_FUNCTION_NETWORK_API_KEY>
      roles:
        - autocomplete
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "tabAutocompleteModel": {
      "title": "Deepseek Coder 6.7b",
      "provider": "function-network",
      "model": "thebloke/deepseek-coder-6.7b-base-awq",
      "apiKey": "<YOUR_FUNCTION_NETWORK_API_KEY>"
    }
  }
  ```
  </Tab>
</Tabs>

## Embeddings model

Function Network supports a number of models for embeddings. We recommend using baai/bge-base-en-v1.5.

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: BGE Base En
      provider: function-network
      model: baai/bge-base-en-v1.5
      apiKey: <YOUR_FUNCTION_NETWORK_API_KEY>
      roles:
        - embed
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "embeddingsProvider": {
      "provider": "function-network",
      "model": "baai/bge-base-en-v1.5",
      "apiKey": "<YOUR_FUNCTION_NETWORK_API_KEY>"
    }
  }
  ```
  </Tab>
</Tabs>

[Click here](https://docs.function.network/models-supported/embeddings) to see a list of embeddings model providers.

## Reranking model

Function Network currently does not offer any reranking models.
