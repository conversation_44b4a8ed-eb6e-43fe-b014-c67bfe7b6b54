---
title: "Overview"
description: "Explore Continue's advanced capabilities for power users and complex development scenarios."
---

## Context Providers

Master how Continue understands your codebase with powerful context providers for code, documentation, and more.

[Explore Context Providers →](/customize/custom-providers)

## Context Integration

Specialized context features for codebase understanding and documentation integration.

[Browse Context Features →](/customization/overview#codebase-context)

## Deep Dives

Detailed technical explanations of Continue's internal workings and advanced configuration options.

[Read Deep Dives →](/customization/overview#configuration)

## Model Providers

Configure and optimize different AI model providers for your specific needs and infrastructure.

[Configure Providers →](/customization/models#openai)

## Model Roles

Understand how different models can be assigned specific roles in your development workflow.

[Learn Model Roles →](/customize/model-roles)

## Reference

Complete configuration reference and API documentation.

[View Reference →](/reference)

## Troubleshooting

Solutions to common issues and debugging techniques.

[Get Help →](/troubleshooting)

---

These advanced topics help you get the most out of Continue in complex development environments.
