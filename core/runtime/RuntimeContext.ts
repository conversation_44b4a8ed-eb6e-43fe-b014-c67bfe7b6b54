import { User<PERSON> } from "../protocol/model/UserVO";
import { IDE, IdeInfo } from "../index";

export class RuntimeContext {
  static ide?: IDE;
  static user?: UserVO;
  static ideInfo?: IdeInfo;

  static async setup(ide: IDE) {
    RuntimeContext.ide = ide;
    RuntimeContext.user = await ide.getLoginInfo();
    RuntimeContext.ideInfo = await ide.getIdeInfo();
  }

  static setupUser(user: UserVO) {
    RuntimeContext.user = user;
    console.debug(`user changed: ${user?.empId}, from auth/update`);
  }

  static get empId() {
    return RuntimeContext.user?.empId ?? "";
  }
}
