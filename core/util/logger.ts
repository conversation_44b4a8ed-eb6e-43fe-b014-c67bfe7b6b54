import { getLogsDirPath } from "./paths";
import pino from "pino";
import { createStream, Options } from "rotating-file-stream";

const options: Options = {
  size: "10M", // 单个日志文件最大 10MB
  rotate: 10, // 保留最近的 10 个日志文件
  interval: "1d", // 每天轮转 (也可以是 '1h', '30m' 等)
  path: getLogsDirPath(),
  compress: "gzip", // 对轮转后的旧日志文件进行 gzip 压缩
};

export const promptStream = createStream("prompt.log", options);

export const logger = pino(createStream("core.log", options));

export function setupCoreLogger(isTcp: boolean) {
  const { log } = console;
  if (isTcp) {
    logger.level = "debug";
  } else {
    logger.level = "info";
  }

  const debugLogger = (message: any, ...optionalParams: any[]) => {
    logger.debug({ ...optionalParams }, message);
    if (isTcp) log(message, ...optionalParams);
  };

  const infoLogger = (message: any, ...optionalParams: any[]) => {
    logger.info({ ...optionalParams }, message);
    if (isTcp) log(message, ...optionalParams);
  };

  const warnLogger = (message: any, ...optionalParams: any[]) => {
    logger.warn({ ...optionalParams }, message);
    if (isTcp) log(message, ...optionalParams);
  };

  const errorLogger = (message: any, ...optionalParams: any[]) => {
    logger.error({ ...optionalParams }, message);
    if (isTcp) log(message, ...optionalParams);
  };

  console.log = infoLogger;
  console.info = infoLogger;
  console.error = errorLogger;
  console.warn = warnLogger;
  console.debug = debugLogger;
  console.log("[info] Starting AIMI core...");
}
