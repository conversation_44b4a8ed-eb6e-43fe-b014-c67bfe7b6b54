import { Mutex } from "async-mutex";
import { open } from "sqlite";
import sqlite3 from "sqlite3";

import {
  DatabaseConnection,
  truncateSqliteLikePattern,
} from "../../indexing/refreshIndex.js";
import { getTabAutocompleteCacheSqlitePath } from "../../util/paths.js";

/**
 * 快速内存缓存层，借鉴 vscode-copilot 的多层缓存策略
 */
class MemoryCache {
  private cache = new Map<string, { value: string; timestamp: number }>();
  private readonly maxSize = 100; // 内存缓存最大条目数
  private readonly maxAge = 5 * 60 * 1000; // 5分钟过期

  get(key: string): string | undefined {
    const item = this.cache.get(key);
    if (!item) return undefined;

    // 检查是否过期
    if (Date.now() - item.timestamp > this.maxAge) {
      this.cache.delete(key);
      return undefined;
    }

    return item.value;
  }

  set(key: string, value: string): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, { value, timestamp: Date.now() });
  }

  clear(): void {
    this.cache.clear();
  }
}

export class AutocompleteLruCache {
  private static capacity = 1000;
  private mutex = new Mutex();
  private memoryCache = new MemoryCache();

  constructor(private db: DatabaseConnection) {}

  static async get(): Promise<AutocompleteLruCache> {
    const db = await open({
      filename: getTabAutocompleteCacheSqlitePath(),
      driver: sqlite3.Database,
    });

    await db.exec("PRAGMA busy_timeout = 3000;");

    await db.run(`
      CREATE TABLE IF NOT EXISTS cache (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        timestamp INTEGER NOT NULL
      )
    `);

    const cache = new AutocompleteLruCache(db);

    // 启动时清理一次过期缓存
    try {
      await cache.cleanup();
    } catch (e) {
      console.warn("Initial cache cleanup failed:", e);
    }

    return cache;
  }

  async get(prefix: string): Promise<string | undefined> {
    // 首先检查内存缓存
    const truncatedPrefix = truncateSqliteLikePattern(prefix);
    const memoryResult = this.memoryCache.get(truncatedPrefix);
    if (memoryResult !== undefined) {
      return memoryResult;
    }

    // 如果内存缓存中没有，查询数据库
    try {
      const result = await this.db.get(
        "SELECT key, value FROM cache WHERE ? LIKE key || '%' ORDER BY LENGTH(key) DESC LIMIT 1",
        truncatedPrefix,
      );

      // 验证缓存的补全是否有效
      if (
        result &&
        result.value.startsWith(truncatedPrefix.slice(result.key.length))
      ) {
        await this.db.run(
          "UPDATE cache SET timestamp = ? WHERE key = ?",
          Date.now(),
          truncatedPrefix,
        );

        const completionResult = result.value.slice(
          truncatedPrefix.length - result.key.length,
        );

        // 将结果添加到内存缓存
        this.memoryCache.set(truncatedPrefix, completionResult);

        return completionResult;
      }
    } catch (e) {
      // 捕获 SQLite 模式复杂性错误
      console.error(e);
    }

    return undefined;
  }

  async put(prefix: string, completion: string) {
    const release = await this.mutex.acquire();
    const truncatedPrefix = truncateSqliteLikePattern(prefix);

    try {
      // 首先更新内存缓存
      this.memoryCache.set(truncatedPrefix, completion);

      await this.db.run("BEGIN TRANSACTION");

      try {
        const result = await this.db.get(
          "SELECT key FROM cache WHERE key = ?",
          truncatedPrefix,
        );

        if (result) {
          await this.db.run(
            "UPDATE cache SET value = ?, timestamp = ? WHERE key = ?",
            completion,
            Date.now(),
            truncatedPrefix,
          );
        } else {
          const count = await this.db.get(
            "SELECT COUNT(*) as count FROM cache",
          );

          if (count.count >= AutocompleteLruCache.capacity) {
            await this.db.run(
              "DELETE FROM cache WHERE key = (SELECT key FROM cache ORDER BY timestamp ASC LIMIT 1)",
            );
          }

          await this.db.run(
            "INSERT OR REPLACE INTO cache (key, value, timestamp) VALUES (?, ?, ?)",
            truncatedPrefix,
            completion,
            Date.now(),
          );
        }

        await this.db.run("COMMIT");
      } catch (error) {
        await this.db.run("ROLLBACK");
        throw error;
      }
    } catch (e) {
      console.error("Error creating transaction: ", e);
    } finally {
      release();
    }
  }

  /**
   * 清除过期的缓存条目
   */
  async cleanup(): Promise<void> {
    const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

    try {
      await this.db.run("DELETE FROM cache WHERE timestamp < ?", oneWeekAgo);

      // 清理内存缓存
      this.memoryCache.clear();
    } catch (e) {
      console.error("Error during cache cleanup:", e);
    }
  }
}

export default AutocompleteLruCache;
