package com.taobao.mc.aimi.util

import com.intellij.openapi.util.IconLoader
import com.intellij.ui.JBColor
import javax.swing.Icon

object AIMIIcons {

    @JvmField
    val AIMI = getAimiIcon()

    @JvmField
    val Close = IconLoader.getIcon("/icons/close.svg", javaClass)

    private fun getAimiIcon(): Icon {
        val iconPath = if (JBColor.isBright())
            "/icons/aimi.svg"
        else
            "/icons/aimi_dark.svg"
        return IconLoader.getIcon(iconPath, javaClass)
    }
}