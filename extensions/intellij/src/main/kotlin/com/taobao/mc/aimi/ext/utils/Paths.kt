package com.taobao.mc.aimi.ext.utils

import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.extensions.PluginId
import com.taobao.mc.aimi.ext.constants.AIMIConstants
import java.nio.file.Path
import java.nio.file.Paths

/**
 * Gets the path to the Continue plugin directory
 *
 * @return Path to the plugin directory
 * @throws Exception if the plugin is not found
 */
fun getAIMIPluginPath(): Path {
    val pluginDescriptor =
        PluginManagerCore.getPlugin(PluginId.getId(AIMIConstants.PLUGIN_ID)) ?: throw Exception("Plugin not found")
    return pluginDescriptor.pluginPath
}

/**
 * Gets the path to the Continue ext directory with target platform
 *
 * @return Path to the Continue ext directory with target platform
 * @throws Exception if the plugin is not found
 */
fun getAIMICorePath(): String {
    val pluginPath = getAIMIPluginPath()
    val corePath = Paths.get(pluginPath.toString(), "core").toString()
    val target = getOsAndArchTarget()
    return Paths.get(corePath, target).toString()
}

/**
 * Gets the path to the Continue binary executable
 *
 * @return Path to the Continue binary executable
 * @throws Exception if the plugin is not found
 */
fun getAIMIBinaryPath(): String {
    val targetPath = getAIMICorePath()
    val os = getOS()
    val exeSuffix = if (os == OS.WINDOWS) ".exe" else ""
    return Paths.get(targetPath, "aimi-binary-intellij$exeSuffix").toString()
}

/**
 * Gets the path to the Ripgrep executable
 *
 * @return Path to the Ripgrep executable
 * @throws Exception if the plugin is not found
 */
fun getRipgrepPath(): String {
    val targetPath = getAIMICorePath()
    val os = getOS()
    val exeSuffix = if (os == OS.WINDOWS) ".exe" else ""
    return Paths.get(targetPath, "rg$exeSuffix").toString()
}