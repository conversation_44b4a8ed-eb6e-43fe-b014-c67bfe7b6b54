package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.auth.ContinueAuthService
import com.taobao.mc.aimi.ext.auth.ControlPlaneSessionInfo
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.protocol.GetControlPlaneSessionInfoParams
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.types.MessageTypes.ToIDE

/**
 * 认证相关的消息处理器
 * 包括控制平面会话信息、登录登出、遥测设置等
 */

/**
 * 设置控制平面会话信息处理器
 */
class SetControlPlaneSessionInfoHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.SetControlPlaneSessionInfo

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ControlPlaneSessionInfo>(dataElement)
        val authService = service<ContinueAuthService>()
        authService.setControlPlaneSessionInfo(params)
        respond(null)
    }
}

/**
 * 获取控制平面会话信息处理器
 */
class GetControlPlaneSessionInfoHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetControlPlaneSessionInfo

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<GetControlPlaneSessionInfoParams>(dataElement)
        val authService = service<ContinueAuthService>()

        if (params.silent) {
            val sessionInfo = authService.loadControlPlaneSessionInfo()
            respond(sessionInfo)
        } else {
            authService.startAuthFlow(project, params.useOnboarding)
            respond(null)
        }
    }
}

/**
 * 登出控制平面处理器
 */
class LogoutOfControlPlaneHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.LogoutOfControlPlane

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val authService = service<ContinueAuthService>()
        authService.signOut()
        respond(null)
    }
}

/**
 * 是否启用遥测处理器
 */
class IsTelemetryEnabledHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.IsTelemetryEnabled

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val isEnabled = ide.isTelemetryEnabled()
        respond(isEnabled)
    }
}
