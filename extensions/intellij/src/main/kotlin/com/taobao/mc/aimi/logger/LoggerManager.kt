package com.taobao.mc.aimi.logger

import com.intellij.openapi.diagnostic.LogLevel
import com.intellij.openapi.diagnostic.Logger

object LoggerManager {
    private val isDebugMode = System.getenv("USE_TCP")?.toBoolean() ?: false
    private const val MODULE = "AIMI-IDE"

    fun getLogger(tag: String): Logger {
        return Logger.getInstance("${MODULE}.${tag}").apply {
            if (isDebugMode) setLevel(LogLevel.DEBUG)
        }
    }

    fun getLogger(clazz: Class<*>): Logger {
        return getLogger(clazz.simpleName)
    }
}