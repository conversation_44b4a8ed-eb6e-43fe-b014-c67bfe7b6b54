package com.taobao.mc.aimi.ext.core

import com.intellij.openapi.project.Project
import com.intellij.openapi.util.io.StreamUtil
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.jetbrains.jsonSchema.extension.JsonSchemaFileProvider
import com.jetbrains.jsonSchema.extension.JsonSchemaProviderFactory
import com.jetbrains.jsonSchema.extension.SchemaType
import com.taobao.mc.aimi.ext.activities.AIMIPluginStartupActivity
import com.taobao.mc.aimi.ext.constants.getAIMIGlobalPath
import java.io.File
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.nio.file.Paths

class ConfigRcJsonSchemaProviderFactory : JsonSchemaProviderFactory {
    override fun getProviders(project: Project): MutableList<JsonSchemaFileProvider> {
        return mutableListOf(ConfigRcJsonSchemaFileProvider())
    }
}

class ConfigRcJsonSchemaFileProvider : JsonSchemaFileProvider {
    override fun isAvailable(file: VirtualFile): Boolean {
        return file.name == ".aimi_rc.json"
    }

    override fun getName(): String {
        return ".aimi_rc.json"
    }

    override fun getSchemaFile(): VirtualFile? {
        val schemaName = "aimi_rc_schema.json"
        AIMIPluginStartupActivity::class.java.getClassLoader().getResourceAsStream(schemaName)
            .use { `is` ->
                if (`is` == null) {
                    throw IOException("Resource not found: $schemaName")
                }
                val content = StreamUtil.readText(`is`, StandardCharsets.UTF_8)
                val filepath = Paths.get(getAIMIGlobalPath(), schemaName).toString()
                File(filepath).writeText(content)
                return LocalFileSystem.getInstance().findFileByPath(filepath)
            }
    }

    override fun getSchemaType(): SchemaType {
        return SchemaType.embeddedSchema
    }

}
