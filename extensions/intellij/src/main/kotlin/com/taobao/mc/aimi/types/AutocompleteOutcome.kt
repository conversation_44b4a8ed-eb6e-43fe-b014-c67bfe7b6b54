package com.taobao.mc.aimi.types

data class AutocompleteOutcome(
    // var editor: Editor,
    // var offset: Int,
    // AutocompleteOutcome specific properties
    val accepted: Boolean? = null,
    val time: Long,
    val prefix: String,
    val suffix: String,
    val prompt: String,
    val completion: String?,
    val modelProvider: String,
    val modelName: String,
    val completionOptions: Any,
    val cacheHit: Boolean,
    val numLines: Int,
    val filepath: String,
    val gitRepo: String? = null,
    val completionId: String,
    val uniqueId: String,
    val timestamp: Long,
    val remoteRequestId: String? = null,
    val localRequestId: String? = null,
    val startTime: Long? = null,
    val endTime: Long? = null,

    // TabAutocompleteOptions properties
    val disable: Boolean,
    val maxPromptTokens: Int,
    val debounceDelay: Int,
    val maxSuffixPercentage: Double,
    val prefixPercentage: Double,
    val transform: Boolean? = null,
    val template: String? = null,
    val multilineCompletions: MultilineCompletions,
    val slidingWindowPrefixPercentage: Double,
    val slidingWindowSize: Int,
    val useCache: Boolean,
    val onlyMyCode: Boolean,
    val useRecentlyEdited: Boolean,
    val disableInFiles: List<String>? = null,
    val useImports: Boolean? = null
)

enum class MultilineCompletions {
    ALWAYS,
    NEVER,
    AUTO
}