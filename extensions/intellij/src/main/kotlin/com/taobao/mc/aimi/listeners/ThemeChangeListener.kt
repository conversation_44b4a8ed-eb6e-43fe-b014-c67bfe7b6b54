package com.taobao.mc.aimi.listeners

import com.intellij.ide.ui.LafManager
import com.intellij.ide.ui.LafManagerListener
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.taobao.mc.aimi.ext.actions.aimiToolWindow
import com.taobao.mc.aimi.util.AIMIIcons

/**
 * 主题变更监听器
 */
class ThemeChangeListener : LafManagerListener {
    override fun lookAndFeelChanged(source: LafManager) {
        // 当主题变化时，更新所有工具窗口的图标
        for (project in ProjectManager.getInstance().openProjects) {
            updateToolWindowIcons(project)
        }
    }

    companion object {
        fun updateToolWindowIcons(project: Project) {
            ApplicationManager.getApplication().invokeLater {
                project.aimiToolWindow?.setIcon(AIMIIcons.AIMI)
            }
        }
    }
}
