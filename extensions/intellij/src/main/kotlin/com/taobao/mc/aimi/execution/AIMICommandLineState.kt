package com.taobao.mc.aimi.execution

import com.intellij.execution.configurations.CommandLineState
import com.intellij.execution.configurations.GeneralCommandLine
import com.intellij.execution.process.KillableProcessHandler
import com.intellij.execution.process.OSProcessHandler
import com.intellij.execution.process.ProcessHandler
import com.intellij.execution.process.ProcessTerminatedListener
import com.intellij.execution.runners.ExecutionEnvironment
import com.intellij.openapi.util.SystemInfo
import com.taobao.mc.aimi.logger.LoggerManager
import java.io.File
import java.nio.charset.StandardCharsets

class AIMICommandLineState(
    environment: ExecutionEnvironment,
    private val configuration: AIMIRunConfiguration
) : CommandLineState(environment) {

    override fun startProcess(): ProcessHandler {
        // 获取脚本路径和工作目录
        val command = configuration.command
        val workingDirectory = configuration.workingDirectory.takeIf { it.isNotEmpty() }
            ?: environment.project.basePath ?: ""

        // 创建命令行，根据不同的 shell 环境进行配置
        val commandLine = createShellCommandLine(command, workingDirectory)

        // 创建进程处理器
        val processHandler = if (SystemInfo.isWindows) {
            KillableProcessHandler(commandLine)
        } else {
            OSProcessHandler(commandLine)
        }

        // 添加进程终止监听器
        ProcessTerminatedListener.attach(processHandler)

        // 创建并附加 AIMITerminalInfo
        // val existingTerminalInfo = AIMITerminalInfo.findActiveTerminal(environment.project)
        val terminalInfo = AIMITerminalInfo(
            toolUserId = configuration.toolUserId,
            terminalId = configuration.terminalId,
            command = commandLine.commandLineString,
            workspaceDir = workingDirectory
        )

        // 附加到进程处理器
        terminalInfo.attach(processHandler)

        return processHandler
    }

    companion object {
        private val logger = LoggerManager.getLogger(AIMICommandLineState::class.java)

        /**
         * 创建适合当前系统和用户 shell 环境的命令行
         */
        fun createShellCommandLine(command: String, workingDirectory: String): GeneralCommandLine {
            val commandLine = GeneralCommandLine()
            commandLine.charset = StandardCharsets.UTF_8
            commandLine.withWorkDirectory(workingDirectory)

            // 根据操作系统选择不同的处理方式
            when {
                SystemInfo.isWindows -> {
                    // Windows 环境
                    setupWindowsCommandLine(commandLine, command)
                }

                SystemInfo.isMac || SystemInfo.isLinux -> {
                    // macOS 或 Linux 环境
                    setupUnixCommandLine(commandLine, command)
                }

                else -> {
                    // 其他操作系统，默认使用 bash
                    commandLine.exePath = "/bin/bash"
                    commandLine.addParameter("-c")
                    commandLine.addParameter(command)
                }
            }

            // 添加环境变量
            // configuration.environmentVariables?.forEach { (key, value) ->
            //     commandLine.withEnvironment(key, value)
            // }

            return commandLine
        }

        /**
         * 设置 Windows 环境下的命令行
         */
        private fun setupWindowsCommandLine(commandLine: GeneralCommandLine, command: String) {
            // 检查是否有 WSL
            val hasWsl = File("C:\\Windows\\System32\\wsl.exe").exists()

            if (hasWsl) {
                // 使用 WSL 执行
                commandLine.exePath = "wsl"
                commandLine.addParameter("-e")
                commandLine.addParameter("bash")
                commandLine.addParameter("-c")
                commandLine.addParameter(command)
            } else {
                // 使用 CMD 或 PowerShell
                val isPowerShell = false /*configuration.useWindowsPowerShell*/

                if (isPowerShell) {
                    commandLine.exePath = "powershell.exe"
                    commandLine.addParameter("-ExecutionPolicy")
                    commandLine.addParameter("Bypass")
                    commandLine.addParameter("-File")
                    commandLine.addParameter(command)
                } else {
                    commandLine.exePath = "cmd.exe"
                    commandLine.addParameter("/c")
                    commandLine.addParameter(command)
                }
            }
        }

        /**
         * 设置 Unix 环境下的命令行
         */
        private fun setupUnixCommandLine(commandLine: GeneralCommandLine, command: String) {
            // 检测用户的默认 shell
            val userShell = detectUserShell()

            when {
                userShell.contains("zsh") -> {
                    commandLine.exePath = userShell
                    commandLine.addParameter("-c")
                    commandLine.addParameter(command)
                    logger.debug("Using zsh: $userShell")
                }

                userShell.contains("bash") -> {
                    commandLine.exePath = userShell
                    commandLine.addParameter("-c")
                    commandLine.addParameter(command)
                    logger.debug("Using bash: $userShell")
                }

                userShell.contains("fish") -> {
                    commandLine.exePath = userShell
                    commandLine.addParameter("-c")
                    commandLine.addParameter(command)
                    logger.debug("Using fish: $userShell")
                }

                else -> {
                    // 默认使用 /bin/sh
                    commandLine.exePath = "/bin/sh"
                    commandLine.addParameter("-c")
                    commandLine.addParameter(command)
                    logger.debug("Using default shell: /bin/sh")
                }
            }
        }

        /**
         * 检测用户的默认 shell
         */
        private fun detectUserShell(): String {
            return try {
                val process = Runtime.getRuntime().exec(arrayOf("sh", "-c", "echo \$SHELL"))
                val shell = process.inputStream.bufferedReader().readText().trim()
                process.waitFor()

                shell.ifEmpty {
                    // 尝试从 /etc/passwd 获取
                    val username = System.getProperty("user.name")
                    val process2 = Runtime.getRuntime().exec(arrayOf("sh", "-c", "grep '^$username:' /etc/passwd | cut -d: -f7"))
                    val shellFromPasswd = process2.inputStream.bufferedReader().readText().trim()
                    process2.waitFor()

                    shellFromPasswd.ifEmpty {
                        // 默认返回 bash
                        "/bin/bash"
                    }
                }
            } catch (e: Exception) {
                logger.warn("Failed to detect user shell: ${e.message}")
                "/bin/bash" // 默认返回 bash
            }
        }
    }
}