package com.taobao.mc.aimi.ext.core

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.core.process.AIMIBinaryProcess
import com.taobao.mc.aimi.ext.core.process.AIMIProcessHandler
import com.taobao.mc.aimi.ext.core.process.AIMISocketProcess
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.uuid
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.protocol.IdeProtocolClient
import com.taobao.mc.aimi.types.MessageTypes
import kotlinx.coroutines.CoroutineScope

class CoreMessenger(
    private val project: Project,
    private val ideProtocolClient: IdeProtocolClient,
    val coroutineScope: CoroutineScope,
    private val onExit: () -> Unit
) {
    private val gson = Gson()
    private val responseListeners = mutableMapOf<String, (JsonObject?) -> Unit>()
    private val process = startAIMIProcess()
    private val logger = LoggerManager.getLogger(CoreMessenger::class.java)

    private fun request(messageType: String, data: Any?, messageId: String? = uuid(), onResponse: (JsonObject?) -> Unit = {}) {
        val id = messageId ?: uuid()
        val message = gson.toJson(mapOf("messageId" to id, "messageType" to messageType, "data" to data))
        responseListeners[id] = onResponse
        process.write(message)
    }

    private fun startAIMIProcess(): AIMIProcessHandler {
        val isTcp = System.getenv("USE_TCP")?.toBoolean() ?: false
        val process = if (isTcp)
            AIMISocketProcess()
        else
            AIMIBinaryProcess(project, onExit)
        return AIMIProcessHandler(coroutineScope, process, ::handleMessage)
    }

    fun request(messageType: MessageTypes.ToCore, data: Any?, messageId: String? = uuid(), onResponse: (JsonObject?) -> Unit = {}) {
        request(messageType.type, data, messageId, onResponse)
    }

    private fun handleMessage(json: String) {
        val responseMap = runCatching {
            JsonParser.parseString(json).asJsonObject
        }.onFailure {
            logger.warnWithDebug("Error parsing message: ", it)
        }.getOrNull() ?: run {
            logger.warn("Error parsing message: $json")
            return
        }
        val messageId = responseMap["messageId"].asString
        val messageType = MessageTypes.from(responseMap["messageType"].asString) ?: run {
            logger.warn("Unknown message: $json")
            return
        }
        val data = responseMap["data"]?.asJsonObject

        // IDE listeners
        if (MessageTypes.ideMessageTypes.contains(messageType)) {
            ideProtocolClient.handleMessage(json) { data ->
                val message = gson.toJson(mapOf("messageId" to messageId, "messageType" to messageType, "data" to data))
                process.write(message)
            }
        }

        // Forward to webview
        if (MessageTypes.webviewMessageTypes.contains(messageType)) {
            val continuePluginService = project.service<AIMIPluginService>()
            continuePluginService.sendToWebview(messageType, responseMap["data"])
        }

        // Responses for messageId
        responseListeners[messageId]?.let { listener ->
            listener(data)
            val done = data?.get("done")?.asBoolean ?: false

            if (done) {
                responseListeners.remove(messageId)
            }
        }
    }

    fun killSubProcess() {
        responseListeners.clear()
        process.close()
    }
}