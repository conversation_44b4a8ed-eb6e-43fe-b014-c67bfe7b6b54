package com.taobao.mc.aimi.psi

import com.intellij.lang.javascript.psi.JSExpression
import com.intellij.lang.javascript.psi.JSFunctionExpression
import com.intellij.lang.javascript.psi.JSReferenceExpression
import com.intellij.openapi.components.Service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.intellij.openapi.vfs.toNioPathOrNull
import com.intellij.psi.*
import com.taobao.mc.aimi.ext.core.UriUtils
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.SymbolInfo
import org.jetbrains.kotlin.psi.KtDotQualifiedExpression
import org.jetbrains.kotlin.psi.KtExpression
import org.jetbrains.kotlin.psi.KtLambdaExpression
import kotlin.io.path.relativeTo

/**
 * 对象信息
 */
data class ObjectInfo constructor(
    val name: String,                    // 对象名称（如：this, super, 变量名等）
    val type: String,                    // 类型名称
    val qualifiedType: String?,          // 完全限定类型名
    val kind: ObjectKind,                // 对象种类
    val element: PsiElement?,            // 对应的PSI元素
    val members: List<MemberInfo>,       // 可访问的成员
    val isNullable: Boolean = false,     // 是否可为null（主要用于Kotlin）
    val confidence: Double = 1.0,        // 匹配置信度
    val filepath: String? = null,        // 源码/文件路径
    val isLambdaParameter: Boolean = false, // 是否为Lambda参数
    val lambdaContext: String? = null,   // Lambda上下文信息,
    val content: String? = null,           // 类的内容, 给特殊的类准备的, 比如 data class/ enum class / sealed class 等
) {
    fun toSymbolInfo(project: Project): SymbolInfo {
        val memberContent = members.joinToString("\n") { member ->
            val prefix = member.visibility
            when (member.kind) {
                MemberKind.FIELD -> {
                    "$prefix ${member.name}: ${member.type}"
                }

                MemberKind.PROPERTY -> {
                    "$prefix ${member.name}: ${member.type}"
                }

                MemberKind.METHOD -> {
                    "$prefix ${member.signature}"
                }

                MemberKind.CONSTRUCTOR -> {
                    "$prefix ${member.signature}"
                }
            }
        }

        val path = filepath?.let {
            project.guessProjectDir()?.toNioPathOrNull()?.let { projectPath ->
                UriUtils.uriToVirtualFile(filepath)?.toNioPathOrNull()?.relativeTo(projectPath)?.toString()
            }
        } ?: ""
        return SymbolInfo(
            filepath = path.ifEmpty { qualifiedType ?: type },
            content = memberContent + (this.content ?: "")
        )
    }
}

/**
 * 对象种类
 */
enum class ObjectKind {
    THIS,           // this对象
    SUPER,          // super对象
    LOCAL_VARIABLE, // 局部变量
    PARAMETER,      // 方法参数
    FIELD,          // 字段
    STATIC_FIELD,   // 静态字段
    METHOD_CALL,    // 方法调用结果
    EXPRESSION,     // 表达式结果
    LAMBDA_PARAMETER, // Lambda参数
    LAMBDA_RECEIVER,  // Lambda接收者
    SPECIAL_CLASS,  // 特殊类: data class / enum class / sealed class
}

/**
 * 成员信息
 */
data class MemberInfo(
    val name: String,
    val type: String,
    val kind: MemberKind,
    val visibility: String,
    val isStatic: Boolean = false,
    val signature: String? = null,
    val documentation: String? = null
)

enum class MemberKind {
    FIELD, METHOD, PROPERTY, CONSTRUCTOR
}

/**
 * 对象类型解析器接口
 * 定义统一的类型解析契约，支持多语言和 Lambda 表达式
 */
interface IObjectTypeResolver {
    /**
     * 解析光标位置的对象类型
     */
    fun resolveObjectAtCursor(editor: Editor): List<ObjectInfo>

    /**
     * 查找最佳匹配的对象
     */
    fun findBestMatchingObject(editor: Editor): ObjectInfo?

    /**
     * 获取对象的可访问成员
     */
    fun getAccessibleMembers(objectInfo: ObjectInfo, includePrivate: Boolean = false): List<MemberInfo>

    /**
     * 根据名称过滤成员
     */
    fun filterMembersByName(members: List<MemberInfo>, namePrefix: String): List<MemberInfo>

    /**
     * 检测是否在 Lambda 表达式内部
     */
    fun isInLambdaContext(element: PsiElement): Boolean

    /**
     * 解析 Lambda 上下文中的对象
     */
    fun resolveLambdaContext(element: PsiElement): List<ObjectInfo>
}

interface KotlinResolver {
    fun resolveObjects(element: PsiElement, offset: Int): List<ObjectInfo>
    fun resolveLambdaParameters(lambdaExpression: KtLambdaExpression): List<ObjectInfo>
}

/**
 * 对象类型解析器
 * 专门用于识别光标位置最合适的对象及其类型信息
 * 支持多语言和 Lambda 表达式兼容性
 */
@Service(Service.Level.PROJECT)
class ObjectTypeResolver(private val project: Project) : IObjectTypeResolver {
    private val logger = LoggerManager.getLogger(javaClass)

    // 语言特定的解析器
    val javaResolver by lazy { JavaObjectTypeResolver(project, this) }
    val kotlinResolver by lazy {
        if (useK2Model()) K2Resolver(project, this)
        else KotlinObjectTypeResolver(project, this)
    }
    val javascriptResolver by lazy { JavaScriptObjectTypeResolver(project, this) }

    /**
     * 解析光标位置的对象类型
     */
    override fun resolveObjectAtCursor(editor: Editor): List<ObjectInfo> {
        val offset = editor.caretModel.offset
        val document = editor.document
        val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document) ?: return emptyList()

        val element = psiFile.findElementAt(offset) ?: return emptyList()

        val objects = mutableListOf<ObjectInfo>()
        // 优先检查 Lambda 上下文
        if (isInLambdaContext(element)) {
            val lambdaObjects = resolveLambdaContext(element)
            if (lambdaObjects.isNotEmpty()) {
                // return lambdaObjects
                objects.addAll(lambdaObjects)
            }
        }

        // 委托给语言特定的解析器
        val found = when {
            isJavaFile(psiFile) -> javaResolver.resolveObjects(element, offset)
            isKotlinFile(psiFile) -> kotlinResolver.resolveObjects(element, offset)
            isTypeScriptFile(psiFile) -> javascriptResolver.resolveObjects(element, offset)
            else -> emptyList()
        }
        if (found.isNotEmpty()) {
            objects.addAll(found)
        }
        return objects.sortedByDescending { it.confidence }
    }

    /**
     * 查找最佳匹配的对象
     */
    override fun findBestMatchingObject(editor: Editor): ObjectInfo? {
        val objects = resolveObjectAtCursor(editor)

        // 如果有方法调用接收者，优先返回它
        val methodCallReceiver = objects.find { it.confidence == 1.0 }
        if (methodCallReceiver != null) {
            logger.info("Returning method call receiver: ${methodCallReceiver.name} (${methodCallReceiver.type})")
            return methodCallReceiver
        }

        // 如果有明确的点号上下文，返回点号前的对象
        val offset = editor.caretModel.offset
        val document = editor.document
        val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document) ?: return null
        val element = psiFile.findElementAt(offset) ?: return null

        // 检查是否在点号后面
        val dotContext = when {
            isJavaFile(psiFile) -> findDotContext(element)
            isKotlinFile(psiFile) -> findKotlinDotContext(element)
            isTypeScriptFile(psiFile) -> findTypeScriptDotContext(element)
            else -> null
        }

        if (dotContext != null) {
            // 有明确的点号上下文，返回置信度最高的对象
            return objects.maxByOrNull { it.confidence }
        }

        // 没有点号上下文，优先返回this对象
        val thisObject = objects.find { it.kind == ObjectKind.THIS }
        if (thisObject != null) {
            return thisObject
        }

        // 否则返回置信度最高的对象
        return objects.maxByOrNull { it.confidence }
    }

    /**
     * 获取对象的可访问成员
     */
    override fun getAccessibleMembers(objectInfo: ObjectInfo, includePrivate: Boolean): List<MemberInfo> {
        return if (includePrivate) {
            objectInfo.members
        } else {
            objectInfo.members.filter { it.visibility != "private" }
        }
    }

    /**
     * 根据名称过滤成员
     */
    override fun filterMembersByName(members: List<MemberInfo>, namePrefix: String): List<MemberInfo> {
        return members.filter { it.name.startsWith(namePrefix, ignoreCase = true) }
    }

    /**
     * 检测是否在 Lambda 表达式内部
     */
    override fun isInLambdaContext(element: PsiElement): Boolean {
        var current: PsiElement? = element
        while (current != null) {
            when (current) {
                // Java Lambda 表达式
                is PsiLambdaExpression -> return true
                // Kotlin Lambda 表达式
                is KtLambdaExpression -> return true
                // JavaScript/TypeScript 箭头函数
                // is JSArrowFunction -> return true
                // JavaScript/TypeScript 函数表达式
                is JSFunctionExpression -> return true
            }
            current = current.parent
        }
        return false
    }

    /**
     * 解析 Lambda 上下文中的对象
     */
    override fun resolveLambdaContext(element: PsiElement): List<ObjectInfo> {
        val lambdaObjects = mutableListOf<ObjectInfo>()

        var current: PsiElement? = element
        while (current != null) {
            when (current) {
                // Java Lambda 表达式
                is PsiLambdaExpression -> {
                    lambdaObjects.addAll(javaResolver.resolveLambdaParameters(current))
                    break
                }
                // Kotlin Lambda 表达式
                is KtLambdaExpression -> {
                    lambdaObjects.addAll(kotlinResolver.resolveLambdaParameters(current))
                    break
                }
                // JavaScript/TypeScript 箭头函数
                // is JSArrowFunction -> {
                //     lambdaObjects.addAll(javascriptResolver.resolveLambdaParameters(current))
                //     break
                // }
                // JavaScript/TypeScript 函数表达式
                is JSFunctionExpression -> {
                    lambdaObjects.addAll(javascriptResolver.resolveLambdaParameters(current))
                    break
                }
            }
            current = current.parent
        }

        return lambdaObjects
    }


    // 辅助方法
    private fun isJavaFile(psiFile: PsiFile): Boolean = psiFile.language.id.equals("JAVA", ignoreCase = true)
    private fun isKotlinFile(psiFile: PsiFile): Boolean = psiFile.language.id.equals("kotlin", ignoreCase = true)
    private fun isTypeScriptFile(psiFile: PsiFile): Boolean =
        psiFile.language.id.equals("TypeScript", ignoreCase = true) ||
                psiFile.language.id.equals("JavaScript", ignoreCase = true)

    private fun findDotContext(element: PsiElement): PsiExpression? {
        val parent = element.parent
        if (parent is PsiReferenceExpression && parent.qualifierExpression != null) {
            return parent.qualifierExpression
        }
        return null
    }

    private fun findKotlinDotContext(element: PsiElement): KtExpression? {
        val parent = element.parent
        if (parent is KtDotQualifiedExpression) {
            return parent.receiverExpression
        }
        return null
    }

    private fun findTypeScriptDotContext(element: PsiElement): JSExpression? {
        val parent = element.parent
        if (parent is JSReferenceExpression && parent.qualifier != null) {
            return parent.qualifier
        }
        return null
    }

    private fun useK2Model(): Boolean {
        return runCatching {
            Class.forName("org.jetbrains.kotlin.analysis.api.types.KaType")
            System.getProperty("idea.kotlin.plugin.use.k2").toBoolean()
        }.getOrElse { false }
    }
}