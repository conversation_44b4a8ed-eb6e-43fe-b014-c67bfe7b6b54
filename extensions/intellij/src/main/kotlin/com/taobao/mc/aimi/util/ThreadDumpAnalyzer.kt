package com.taobao.mc.aimi.util

import com.taobao.mc.aimi.logger.LoggerManager
import java.lang.management.ManagementFactory
import java.lang.management.ThreadInfo

/**
 * 线程转储分析工具
 * 提供详细的线程状态分析和UI冻结原因诊断
 */
object ThreadDumpAnalyzer {

    private val logger = LoggerManager.getLogger(ThreadDumpAnalyzer::class.java)

    data class ThreadAnalysisResult(
        val summary: String,
        val edtAnalysis: String,
        val suspiciousThreads: List<SuspiciousThread>,
        val possibleCauses: List<String>,
        val recommendations: List<String>
    )

    data class SuspiciousThread(
        val name: String,
        val state: Thread.State,
        val suspiciousOperations: List<String>,
        val lockInfo: String?,
        val cpuTime: Long = -1
    )

    /**
     * 分析当前的线程转储
     */
    fun analyzeCurrentThreadDump(): ThreadAnalysisResult {
        return try {
            val threadMXBean = ManagementFactory.getThreadMXBean()
            val allThreads = Thread.getAllStackTraces()
            val threadInfos = threadMXBean.dumpAllThreads(true, true)

            val edtAnalysis = analyzeEDTThread(allThreads, threadInfos)
            val suspiciousThreads = findSuspiciousThreads(allThreads, threadInfos)
            val possibleCauses = determinePossibleCauses(allThreads, suspiciousThreads, edtAnalysis)
            val recommendations = generateRecommendations(possibleCauses, suspiciousThreads)
            val summary = generateSummary(allThreads, suspiciousThreads)

            ThreadAnalysisResult(
                summary = summary,
                edtAnalysis = edtAnalysis,
                suspiciousThreads = suspiciousThreads,
                possibleCauses = possibleCauses,
                recommendations = recommendations
            )
        } catch (e: Exception) {
            logger.warn("线程转储分析失败", e)
            ThreadAnalysisResult(
                summary = "分析失败: ${e.message}",
                edtAnalysis = "无法分析EDT线程",
                suspiciousThreads = emptyList(),
                possibleCauses = listOf("分析过程中发生异常"),
                recommendations = listOf("请检查系统状态并重试")
            )
        }
    }

    /**
     * 分析EDT线程
     */
    private fun analyzeEDTThread(
        allThreads: Map<Thread, Array<StackTraceElement>>,
        threadInfos: Array<ThreadInfo>
    ): String {
        val edtThread = allThreads.entries.find {
            it.key.name.contains("AWT-EventQueue") || it.key.name.contains("EDT")
        }

        if (edtThread == null) {
            return "未找到EDT线程"
        }

        val thread = edtThread.key
        val stackTrace = edtThread.value
        val threadInfo = threadInfos.find { it.threadName == thread.name }

        val analysis = StringBuilder()
        analysis.appendLine("EDT线程状态: ${thread.state}")
        analysis.appendLine("线程ID: ${thread.id}")
        analysis.appendLine("优先级: ${thread.priority}")

        // 分析锁信息
        threadInfo?.let { info ->
            if (info.lockName != null) {
                analysis.appendLine("等待锁: ${info.lockName}")
                analysis.appendLine("锁拥有者: ${info.lockOwnerName ?: "未知"}")
            }

            if (info.lockedMonitors.isNotEmpty()) {
                analysis.appendLine("持有的监视器锁: ${info.lockedMonitors.size}个")
            }

            if (info.lockedSynchronizers.isNotEmpty()) {
                analysis.appendLine("持有的同步器: ${info.lockedSynchronizers.size}个")
            }
        }

        // 分析堆栈中的可疑操作
        val suspiciousOps = findSuspiciousOperationsInStack(stackTrace)
        if (suspiciousOps.isNotEmpty()) {
            analysis.appendLine("可疑操作:")
            suspiciousOps.forEach { op ->
                analysis.appendLine("  - $op")
            }
        }

        // 分析堆栈深度
        if (stackTrace.size > 100) {
            analysis.appendLine("警告: 堆栈深度过深 (${stackTrace.size}层)，可能存在递归调用")
        }

        return analysis.toString()
    }

    /**
     * 查找可疑线程
     */
    private fun findSuspiciousThreads(
        allThreads: Map<Thread, Array<StackTraceElement>>,
        threadInfos: Array<ThreadInfo>
    ): List<SuspiciousThread> {
        val suspicious = mutableListOf<SuspiciousThread>()

        allThreads.forEach { (thread, stackTrace) ->
            val threadInfo = threadInfos.find { it.threadName == thread.name }
            val suspiciousOps = findSuspiciousOperationsInStack(stackTrace)
            val lockInfo = threadInfo?.lockName

            // 判断是否可疑
            val isSuspicious = thread.state == Thread.State.BLOCKED ||
                    suspiciousOps.isNotEmpty() ||
                    lockInfo != null ||
                    isHighPriorityThread(thread)

            if (isSuspicious) {
                suspicious.add(
                    SuspiciousThread(
                        name = thread.name,
                        state = thread.state,
                        suspiciousOperations = suspiciousOps,
                        lockInfo = lockInfo,
                        cpuTime = threadInfo?.let { getCpuTime(it.threadId) } ?: -1
                    )
                )
            }
        }

        return suspicious.sortedByDescending { it.suspiciousOperations.size }
    }

    /**
     * 在堆栈中查找可疑操作
     */
    private fun findSuspiciousOperationsInStack(stackTrace: Array<StackTraceElement>): List<String> {
        val operations = mutableListOf<String>()

        stackTrace.forEach { element ->
            val className = element.className
            val methodName = element.methodName

            when {
                // I/O操作
                className.contains("java.io") || className.contains("java.nio") -> {
                    operations.add("I/O操作: $className.$methodName")
                }
                // 网络操作
                className.contains("java.net") || className.contains("http") -> {
                    operations.add("网络操作: $className.$methodName")
                }
                // 数据库操作
                className.contains("sql") || className.contains("jdbc") -> {
                    operations.add("数据库操作: $className.$methodName")
                }
                // 文件操作
                methodName.contains("read") || methodName.contains("write") -> {
                    operations.add("文件操作: $className.$methodName")
                }
                // 同步操作
                methodName == "wait" || methodName.contains("lock") -> {
                    operations.add("同步操作: $className.$methodName")
                }
                // 反射操作
                className.contains("reflect") -> {
                    operations.add("反射操作: $className.$methodName")
                }
                // 类加载
                className.contains("ClassLoader") -> {
                    operations.add("类加载: $className.$methodName")
                }
                // GC相关
                className.contains("gc") || className.contains("GC") -> {
                    operations.add("GC操作: $className.$methodName")
                }
            }
        }

        return operations.distinct()
    }

    /**
     * 判断是否是高优先级线程
     */
    private fun isHighPriorityThread(thread: Thread): Boolean {
        val name = thread.name.lowercase()
        return name.contains("awt-eventqueue") ||
                name.contains("edt") ||
                name.contains("main") ||
                name.contains("idea") ||
                name.contains("intellij") ||
                thread.priority >= Thread.NORM_PRIORITY + 2
    }

    /**
     * 确定可能的原因
     */
    private fun determinePossibleCauses(
        allThreads: Map<Thread, Array<StackTraceElement>>,
        suspiciousThreads: List<SuspiciousThread>,
        edtAnalysis: String
    ): List<String> {
        val causes = mutableListOf<String>()

        // 分析EDT线程状态
        if (edtAnalysis.contains("BLOCKED")) {
            causes.add("EDT线程被阻塞，等待获取锁")
        }
        if (edtAnalysis.contains("WAITING")) {
            causes.add("EDT线程在等待其他线程完成操作")
        }

        // 分析可疑操作
        val allSuspiciousOps = suspiciousThreads.flatMap { it.suspiciousOperations }
        if (allSuspiciousOps.any { it.contains("I/O操作") }) {
            causes.add("检测到I/O操作，可能在EDT线程中执行了阻塞的文件或网络操作")
        }
        if (allSuspiciousOps.any { it.contains("数据库操作") }) {
            causes.add("检测到数据库操作，可能在EDT线程中执行了数据库查询")
        }
        if (allSuspiciousOps.any { it.contains("网络操作") }) {
            causes.add("检测到网络操作，可能在EDT线程中执行了网络请求")
        }

        // 分析线程数量
        val blockedCount = allThreads.count { it.key.state == Thread.State.BLOCKED }
        if (blockedCount > 5) {
            causes.add("大量线程阻塞(${blockedCount}个)，可能存在死锁或严重的锁竞争")
        }

        val runningCount = allThreads.count { it.key.state == Thread.State.RUNNABLE }
        if (runningCount > 20) {
            causes.add("大量线程运行中(${runningCount}个)，可能存在CPU密集型操作")
        }

        return causes
    }

    /**
     * 生成建议
     */
    private fun generateRecommendations(
        possibleCauses: List<String>,
        suspiciousThreads: List<SuspiciousThread>
    ): List<String> {
        val recommendations = mutableListOf<String>()

        if (possibleCauses.any { it.contains("I/O操作") || it.contains("网络操作") }) {
            recommendations.add("将I/O和网络操作移到后台线程执行")
            recommendations.add("使用SwingUtilities.invokeLater()更新UI")
        }

        if (possibleCauses.any { it.contains("数据库操作") }) {
            recommendations.add("使用异步方式执行数据库查询")
            recommendations.add("考虑使用连接池和缓存机制")
        }

        if (possibleCauses.any { it.contains("阻塞") }) {
            recommendations.add("检查锁的使用，避免在EDT线程中获取锁")
            recommendations.add("使用非阻塞的并发工具类")
        }

        if (suspiciousThreads.any { it.suspiciousOperations.any { op -> op.contains("反射") } }) {
            recommendations.add("减少反射操作的使用，或将其移到初始化阶段")
        }

        if (recommendations.isEmpty()) {
            recommendations.add("监控系统资源使用情况")
            recommendations.add("检查是否有内存泄漏或无限循环")
        }

        return recommendations
    }

    /**
     * 生成摘要
     */
    private fun generateSummary(
        allThreads: Map<Thread, Array<StackTraceElement>>,
        suspiciousThreads: List<SuspiciousThread>
    ): String {
        return buildString {
            appendLine("线程总数: ${allThreads.size}")
            appendLine("可疑线程: ${suspiciousThreads.size}")
            appendLine("阻塞线程: ${allThreads.count { it.key.state == Thread.State.BLOCKED }}")
            appendLine("等待线程: ${allThreads.count { it.key.state == Thread.State.WAITING }}")
            appendLine("运行线程: ${allThreads.count { it.key.state == Thread.State.RUNNABLE }}")
        }
    }

    /**
     * 获取线程CPU时间（如果支持）
     */
    private fun getCpuTime(threadId: Long): Long {
        return try {
            val threadMXBean = ManagementFactory.getThreadMXBean()
            if (threadMXBean.isThreadCpuTimeSupported) {
                threadMXBean.getThreadCpuTime(threadId)
            } else {
                -1
            }
        } catch (e: Exception) {
            -1
        }
    }
}