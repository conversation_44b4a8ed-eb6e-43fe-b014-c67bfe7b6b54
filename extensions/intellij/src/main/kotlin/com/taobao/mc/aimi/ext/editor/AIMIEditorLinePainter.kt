package com.taobao.mc.aimi.ext.editor

import com.intellij.openapi.editor.EditorLinePainter
import com.intellij.openapi.editor.LineExtensionInfo
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile

class AIMIEditorLinePainter : EditorLinePainter() {
    override fun getLineExtensions(project: Project, file: VirtualFile, lineNumber: Int): MutableCollection<LineExtensionInfo>? {
        return null
//        return mutableListOf(LineExtensionInfo("Line $lineNumber", null, EffectType.BOLD_DOTTED_LINE, JBColor.BLUE, Font.PLAIN ))
    }
}