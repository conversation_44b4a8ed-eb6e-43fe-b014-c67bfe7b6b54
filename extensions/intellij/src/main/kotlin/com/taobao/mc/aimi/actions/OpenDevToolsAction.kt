package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.taobao.mc.aimi.ext.actions.getContinuePluginService
import com.taobao.mc.aimi.logger.LoggerManager

/**
 * 打开浏览器开发者调试工具的Action
 */
class OpenDevToolsAction : AnAction() {
    private val logger = LoggerManager.getLogger(OpenDevToolsAction::class.java)
    
    override fun actionPerformed(e: AnActionEvent) {
        val continuePluginService = getContinuePluginService(e.project) ?: return
        continuePluginService.activeBrowser?.openDevtools()
    }
}