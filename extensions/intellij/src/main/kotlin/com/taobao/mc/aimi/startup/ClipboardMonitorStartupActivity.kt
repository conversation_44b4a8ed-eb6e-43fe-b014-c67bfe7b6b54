package com.taobao.mc.aimi.startup

import com.intellij.openapi.Disposable
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import com.intellij.openapi.util.Disposer
import com.taobao.mc.aimi.ext.activities.AIMIPluginDisposable
import com.taobao.mc.aimi.services.ClipboardManagerService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ClipboardMonitorStartupActivity : ProjectActivity, Disposable, DumbAware {
    private var service: ClipboardManagerService? = null

    override suspend fun execute(project: Project) {
        // 获取并启动剪贴板监听服务
        val pluginDisposable = AIMIPluginDisposable.getInstance(project)
        Disposer.register(pluginDisposable, this)
        service = ClipboardManagerService.getInstance(project)
        withContext(Dispatchers.IO) {
            service?.startMonitoring()
        }
    }

    override fun dispose() {
        service?.stopMonitoring()
        service = null
    }
}