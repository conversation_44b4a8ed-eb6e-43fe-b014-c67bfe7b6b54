package com.taobao.mc.aimi.psi

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.SymbolInfo

/**
 * 智能补全服务
 * 提供简化的API来获取光标位置最合适的对象和类型信息
 */
@Service(Service.Level.PROJECT)
class SmartCompletionService(private val project: Project) {
    private val logger = LoggerManager.getLogger(javaClass)
    private val objectTypeResolver = project.service<ObjectTypeResolver>()

    /**
     * 补全结果
     */
    data class CompletionResult(
        val targetObject: ObjectInfo?,
        val availableMembers: List<MemberInfo>,
        val suggestions: List<CompletionSuggestion>
    ) {
        override fun toString(): String {
            // availableMembers/suggestions 先转换成 SymbolInfo, 再逐行转换成string, 最后拼接在一起
            val aMembers = availableMembers.map {
                SymbolInfo(
                    content = it.signature ?: it.name,
                    filepath = ""
                )
            }
            val sMembers = suggestions.map {
                SymbolInfo(
                    content = it.signature ?: it.text,
                    filepath = ""
                )
            }
            return "CompletionResult(targetObject=${targetObject?.name}, availableMembers=$aMembers, suggestions=$sMembers)"
        }
    }

    /**
     * 补全建议
     */
    data class CompletionSuggestion(
        val text: String,
        val type: String,
        val kind: SuggestionKind,
        val priority: Int = 0,
        val signature: String? = null,
        val documentation: String? = null
    )

    enum class SuggestionKind {
        METHOD, FIELD, PROPERTY, VARIABLE, KEYWORD
    }

    /**
     * 获取光标位置的智能补全信息
     * 这是主要的API方法
     */
    suspend fun getSmartCompletion(editor: Editor): CompletionResult {
        try {
            // 1. 找到最合适的对象
            val targetObject = objectTypeResolver.findBestMatchingObject(editor)

            if (targetObject == null) {
                logger.debug("No target object found at cursor position")
                return CompletionResult(null, emptyList(), emptyList())
            }

            logger.info("Found target object: ${targetObject.name} of type ${targetObject.type}")

            // 2. 获取可访问的成员
            val accessibleMembers = objectTypeResolver.getAccessibleMembers(targetObject, includePrivate = targetObject.kind == ObjectKind.THIS)

            // 3. 生成补全建议
            val suggestions = generateSuggestions(targetObject, accessibleMembers)

            return CompletionResult(
                targetObject = targetObject,
                availableMembers = accessibleMembers,
                suggestions = suggestions
            )

        } catch (e: Exception) {
            logger.error("Error getting smart completion", e)
            return CompletionResult(null, emptyList(), emptyList())
        }
    }

    /**
     * 获取指定对象的类型信息
     */
    suspend fun getObjectTypeInfo(editor: Editor): ObjectTypeInfo? {
        val targetObject = objectTypeResolver.findBestMatchingObject(editor)
        return targetObject?.let {
            ObjectTypeInfo(
                name = it.name,
                type = it.type,
                qualifiedType = it.qualifiedType,
                kind = it.kind.name,
                isNullable = it.isNullable,
                memberCount = it.members.size
            )
        }
    }

    /**
     * 对象类型信息（简化版）
     */
    data class ObjectTypeInfo(
        val name: String,
        val type: String,
        val qualifiedType: String?,
        val kind: String,
        val isNullable: Boolean,
        val memberCount: Int
    )

    /**
     * 根据前缀过滤补全建议
     */
    suspend fun getFilteredCompletion(editor: Editor, prefix: String): CompletionResult {
        val result = getSmartCompletion(editor)

        if (prefix.isEmpty()) {
            return result
        }

        val filteredMembers = objectTypeResolver.filterMembersByName(result.availableMembers, prefix)
        val filteredSuggestions = result.suggestions.filter {
            it.text.startsWith(prefix, ignoreCase = true)
        }

        return result.copy(
            availableMembers = filteredMembers,
            suggestions = filteredSuggestions
        )
    }

    /**
     * 检查光标位置是否适合进行智能补全
     */
    suspend fun canProvideCompletion(editor: Editor): Boolean {
        return try {
            val objects = objectTypeResolver.resolveObjectAtCursor(editor)
            objects.isNotEmpty()
        } catch (e: Exception) {
            logger.warn("Error checking completion availability", e)
            false
        }
    }

    suspend fun getAvailableObjects(editor: Editor): List<ObjectInfo> {
        return objectTypeResolver.resolveObjectAtCursor(editor)
    }

    /**
     * 生成补全建议
     */
    private fun generateSuggestions(
        targetObject: ObjectInfo,
        members: List<MemberInfo>
    ): List<CompletionSuggestion> {
        val suggestions = mutableListOf<CompletionSuggestion>()

        members.forEach { member ->
            val kind = when (member.kind) {
                MemberKind.METHOD -> SuggestionKind.METHOD
                MemberKind.FIELD -> SuggestionKind.FIELD
                MemberKind.PROPERTY -> SuggestionKind.PROPERTY
                MemberKind.CONSTRUCTOR -> SuggestionKind.METHOD
            }

            val priority = calculatePriority(member, targetObject)

            suggestions.add(
                CompletionSuggestion(
                    text = member.name,
                    type = member.type,
                    kind = kind,
                    priority = priority,
                    signature = member.signature,
                    documentation = member.documentation
                )
            )
        }

        // 按优先级排序
        return suggestions.sortedByDescending { it.priority }
    }

    /**
     * 计算补全建议的优先级
     */
    private fun calculatePriority(
        member: MemberInfo,
        targetObject: ObjectInfo
    ): Int {
        var priority = 0

        // 基础优先级
        when (member.kind) {
            MemberKind.METHOD -> priority += 100
            MemberKind.PROPERTY -> priority += 90
            MemberKind.FIELD -> priority += 80
            MemberKind.CONSTRUCTOR -> priority += 70
        }

        // 可见性加分
        when (member.visibility) {
            "public" -> priority += 20
            "protected" -> priority += 10
            "internal" -> priority += 5
            "private" -> priority += 0
        }

        // 静态成员降低优先级（除非在静态上下文中）
        if (member.isStatic && targetObject.kind != ObjectKind.STATIC_FIELD) {
            priority -= 30
        }

        // 常用方法名加分
        if (member.kind == MemberKind.METHOD) {
            when (member.name) {
                "toString", "equals", "hashCode" -> priority += 15
                "get", "set" -> priority += 10
                "is", "has" -> priority += 8
            }
        }

        return priority
    }

    /**
     * 获取详细的对象信息（用于调试）
     */
    suspend fun getDetailedObjectInfo(editor: Editor): String {
        val objects = objectTypeResolver.resolveObjectAtCursor(editor)

        if (objects.isEmpty()) {
            return "No objects found at cursor position"
        }

        val sb = StringBuilder()
        sb.appendLine("Found ${objects.size} object(s) at cursor position:")
        sb.appendLine()

        objects.forEachIndexed { index, obj ->
            sb.appendLine("Object ${index + 1}:")
            sb.appendLine("  Name: ${obj.name}")
            sb.appendLine("  Type: ${obj.type}")
            sb.appendLine("  Qualified Type: ${obj.qualifiedType ?: "N/A"}")
            sb.appendLine("  Kind: ${obj.kind}")
            sb.appendLine("  Nullable: ${obj.isNullable}")
            sb.appendLine("  Confidence: ${obj.confidence}")
            sb.appendLine("  Members: ${obj.members.size}")

            if (obj.members.isNotEmpty()) {
                sb.appendLine("  Top members:")
                obj.members.take(5).forEach { member ->
                    sb.appendLine("    - ${member.name}: ${member.type} (${member.kind})")
                }
                if (obj.members.size > 5) {
                    sb.appendLine("    ... and ${obj.members.size - 5} more")
                }
            }
            sb.appendLine()
        }

        return sb.toString()
    }

    /**
     * 简化的API：只获取对象名称和类型
     */
    suspend fun getCurrentObjectType(editor: Editor): Pair<String, String>? {
        val targetObject = objectTypeResolver.findBestMatchingObject(editor)
        return targetObject?.let { it.name to it.type }
    }

    /**
     * 简化的API：只获取可用的成员名称
     */
    suspend fun getAvailableMemberNames(editor: Editor): List<String> {
        val result = getSmartCompletion(editor)
        return result.availableMembers.map { it.name }
    }

    /**
     * 简化的API：检查指定成员是否可用
     */
    suspend fun isMemberAvailable(editor: Editor, memberName: String): Boolean {
        val memberNames = getAvailableMemberNames(editor)
        return memberNames.contains(memberName)
    }
}
