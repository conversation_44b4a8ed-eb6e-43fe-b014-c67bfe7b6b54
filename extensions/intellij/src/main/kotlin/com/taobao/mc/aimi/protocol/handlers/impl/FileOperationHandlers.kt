package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.ShowFilePayload
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.protocol.*
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.types.MessageTypes.ToIDE

/**
 * 文件操作相关的消息处理器
 * 包括文件读取、写入、保存、打开、存在性检查等
 */

/**
 * 显示文件处理器
 */
class ShowFileHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ShowFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ShowFilePayload>(dataElement)
        ide.openFile(params.filepath)
        respond(null)
    }
}

/**
 * 读取文件处理器
 */
class ReadFileHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ReadFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ReadFileParams>(dataElement)
        val contents = ide.readFile(params.filepath)
        respond(contents)
    }
}

/**
 * 写入文件处理器
 */
class WriteFileHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.WriteFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<WriteFileParams>(dataElement)
        ide.writeFile(params.path, params.contents)
        respond(null)
    }
}

/**
 * 保存文件处理器
 */
class SaveFileHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.SaveFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<SaveFileParams>(dataElement)
        ide.saveFile(params.filepath)
        respond(null)
    }
}

/**
 * 文件是否存在处理器
 */
class FileExistsHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.FileExists

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<FileExistsParams>(dataElement)
        val exists = ide.fileExists(params.filepath)
        respond(exists)
    }
}

/**
 * 打开文件处理器
 */
class OpenFileHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.OpenFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<OpenFileParams>(dataElement)
        ide.openFile(params.path)
        respond(null)
    }
}

/**
 * 显示虚拟文件处理器
 */
class ShowVirtualFileHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ShowVirtualFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ShowVirtualFileParams>(dataElement)
        ide.showVirtualFile(params.name, params.content)
        respond(null)
    }
}

/**
 * 读取文件范围处理器
 */
class ReadRangeInFileHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ReadRangeInFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ReadRangeInFileParams>(dataElement)
        val contents = ide.readRangeInFile(params.filepath, params.range)
        respond(contents)
    }
}
