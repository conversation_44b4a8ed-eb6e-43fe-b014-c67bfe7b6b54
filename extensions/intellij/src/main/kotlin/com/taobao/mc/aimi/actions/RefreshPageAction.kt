package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.service
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import kotlinx.coroutines.launch

/**
 * 刷新当前浏览器窗口的 Action
 */
class RefreshPageAction : AnAction("刷新页面") {

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val continuePluginService = project.service<AIMIPluginService>()

        // 刷新当前活动的浏览器窗口
        continuePluginService.coroutineScope.launch {
            continuePluginService.activePluginWindow?.reload()
        }
    }

    override fun update(e: AnActionEvent) {
        // 只有在有项目的情况下才启用此 Action
        e.presentation.isEnabled = e.project != null
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}