package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.editor.SelectionModel
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.AcceptOrRejectDiffPayload
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.core.ApplyToFileHandler
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.editor.EditorUtils
import com.taobao.mc.aimi.ext.protocol.ApplyToFileParams
import com.taobao.mc.aimi.ext.protocol.InsertAtCursorParams
import com.taobao.mc.aimi.ext.protocol.ShowDiffParams
import com.taobao.mc.aimi.ext.protocol.ShowLinesParams
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.types.MessageTypes.ToIDE
import com.taobao.mc.aimi.util.mSelectedTextEditor

/**
 * 编辑器相关的消息处理器
 * 包括差异显示、编辑操作、光标操作等
 */

/**
 * 显示差异处理器
 */
class ShowDiffHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ShowDiff

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ShowDiffParams>(dataElement)
        ide.showDiff(params.filepath, params.newContents, params.stepIndex)
        respond(null)
    }
}

/**
 * 接受差异处理器
 */
class AcceptDiffHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.AcceptDiff

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<AcceptOrRejectDiffPayload>(dataElement)
        val filepath = params.filepath

        val editor = EditorUtils.getOrOpenEditor(project, filepath)?.editor

        if (editor != null) {
            diffStreamService.accept(editor)
        }

        respond(null)
    }
}

/**
 * 拒绝差异处理器
 */
class RejectDiffHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.RejectDiff

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<AcceptOrRejectDiffPayload>(dataElement)
        val filepath = params.filepath

        val editor = EditorUtils.getOrOpenEditor(project, filepath)?.editor
        if (editor != null) {
            diffStreamService.reject(editor)
        }
        respond(null)
    }
}

/**
 * 应用到文件处理器
 */
class ApplyToFileMessageHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ApplyToFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ApplyToFileParams>(dataElement)
        params.messageId = messageId

        ApplyToFileHandler.apply(
            project,
            continuePluginService,
            ide,
            params
        )
        respond(null)
    }
}

/**
 * 在光标处插入处理器
 */
class InsertAtCursorHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.InsertAtCursor

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<InsertAtCursorParams>(dataElement)

        ApplicationManager.getApplication().invokeLater {
            val editor = FileEditorManager.getInstance(project).mSelectedTextEditor ?: return@invokeLater
            val selectionModel: SelectionModel = editor.selectionModel

            val document = editor.document
            val startOffset = selectionModel.selectionStart
            val endOffset = selectionModel.selectionEnd

            WriteCommandAction.runWriteCommandAction(project) {
                document.replaceString(startOffset, endOffset, params.text)
            }
        }
        respond(null)
    }
}

/**
 * 获取打开文件处理器
 */
class GetOpenFilesHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetOpenFiles

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val openFiles = ide.getOpenFiles()
        respond(openFiles)
    }
}

/**
 * 获取当前文件处理器
 */
class GetCurrentFileHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetCurrentFile

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val currentFile = ide.getCurrentFile()
        respond(currentFile)
    }
}

/**
 * 获取固定文件处理器
 */
class GetPinnedFilesHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetPinnedFiles

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val pinnedFiles = ide.getPinnedFiles()
        respond(pinnedFiles)
    }
}

/**
 * 显示行处理器
 */
class ShowLinesHandler(
    project: Project,
    ide: IDE,
    continuePluginService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, continuePluginService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ShowLines

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ShowLinesParams>(dataElement)
        ide.showLines(params.filepath, params.startLine, params.endLine)
        respond(null)
    }
}
