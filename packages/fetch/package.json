{"name": "@continuedev/fetch", "version": "1.0.15", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"test": "vitest run", "build": "tsc"}, "author": "<PERSON> and <PERSON>", "license": "Apache-2.0", "dependencies": {"@continuedev/config-types": "file:../config-types", "follow-redirects": "^1.15.6", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.5", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/follow-redirects": "^1.14.4", "vitest": "^3.2.0"}}